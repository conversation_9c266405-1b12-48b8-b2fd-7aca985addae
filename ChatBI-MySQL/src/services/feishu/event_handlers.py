"""
飞书事件处理器模块
负责处理飞书的各种事件，包括消息接收和卡片操作
"""
import json
import asyncio
import lark_oapi as lark
from lark_oapi.event.callback.model.p2_card_action_trigger import (
    P2CardActionTrigger,
    P2CardActionTriggerResponse,
)
from src.utils.logger import logger
from src.services.chatbot.bad_case_service import mark_bad_case
from src.services.feishu.message_apis import (
    reply_simple_text_message,
    after_badcase_mark,
)
from .deduplicator import message_deduplicator
from .user_service import UserService
from .message_parser import MessageParser
from .query_processor import QueryProcessor
from .config import FeishuConfig


class EventHandlers:
    """事件处理器类"""

    @staticmethod
    def handle_card_action_trigger(data: P2CardActionTrigger) -> P2CardActionTriggerResponse:
        """处理卡片按钮点击回调

        Args:
            data: 卡片操作触发数据

        Returns:
            P2CardActionTriggerResponse: 响应对象
        """
        logger.info(lark.JSON.marshal(data))
        conversation_id = data.event.action.value.get("conversation_id")
        card_id = data.event.action.value.get("card_id")
        logger.info(f"用户准备将 conversation_id 标记为 badcase:{conversation_id}")

        if conversation_id:
            # 尝试获取用户信息
            user_name = None
            try:
                user_id = data.event.operator.open_id
                if user_id:
                    user_info_str = UserService.get_user_info(user_id)
                    if user_info_str:
                        user_info = json.loads(user_info_str)
                        user_name = user_info.get('name', '飞书用户')
            except Exception as e:
                logger.warning(f"获取用户信息失败: {e}")
                user_name = '飞书用户'

            mark_bad_case(conversation_id=conversation_id, is_bad_case=True, user_name=user_name)

            # 定义一个异步辅助函数来处理延迟和调用 after_badcase_mark
            async def _delayed_after_badcase_mark_task(current_card_id: str):
                await asyncio.sleep(1.5)  # 异步等待1.5秒
                # after_badcase_mark 是一个阻塞I/O操作（网络请求）
                # 应在线程中运行以避免阻塞 asyncio 事件循环
                await asyncio.to_thread(after_badcase_mark, current_card_id)

            # 创建一个后台任务来执行延迟调用
            if card_id:  # 仅在 card_id 有效时创建任务
                asyncio.create_task(_delayed_after_badcase_mark_task(card_id))
            else:
                logger.warning("card_id 为空，无法调度 after_badcase_mark 的延迟调用")

        resp = {
            "toast": {
                "type": "info",
                "content": "已标记为Badcase，感谢反馈！",
            },
        }
        return P2CardActionTriggerResponse(resp)

    @staticmethod
    async def handle_message_receive(data: lark.im.v1.P2ImMessageReceiveV1) -> None:
        """处理接收到的飞书消息

        Args:
            data: 消息接收数据
        """
        message_data_str = lark.JSON.marshal(data)
        logger.info(f"Async handler processing message: {message_data_str}")

        # 基本验证
        if not data.event.message.content:
            logger.info(f"无需处理: 消息内容为空")
            return

        message_id = data.event.message.message_id
        root_id = data.event.message.root_id or message_id

        # 消息去重检查
        if message_deduplicator.is_message_processed(message_id):
            return
        else:
            message_deduplicator.mark_message_processed(message_id)

        # 检查发送者类型
        if data.event.sender.sender_type != "user":
            logger.info(f"忽略非用户消息: sender_type={data.event.sender.sender_type}")
            return

        user_open_id = data.event.sender.sender_id.open_id
        if not user_open_id:
            logger.info(f"无需处理: 无法获取 user_open_id")
            return

        # 获取用户信息
        user_info_str = UserService.get_user_info(user_id=user_open_id)
        if not user_info_str:
            logger.info(f"获取用户失败: user_open_id:{user_open_id}")
            reply_simple_text_message(
                message_id, "抱歉，无法获取您的用户信息，请稍后再试。"
            )
            return

        user_info_dict = json.loads(user_info_str)
        logger.info(f"获取用户成功:{user_info_dict}")

        # 保存用户信息到数据库
        UserService.upsert_user_info_to_db(user_info_dict, user_open_id)

        # 处理用户信息
        user_info_dict = UserService.process_user_info(user_info_dict)

        # 解析消息内容
        content_data = MessageParser.parse_message_content(data.event.message.content)
        logger.info(f"解析消息内容: {content_data}")

        # 提取文本内容
        user_query = MessageParser.extract_text_from_content(content_data)
        logger.info(f"提取文本内容: {user_query}")

        # 获取聊天类型和提及信息
        chat_type = data.event.message.chat_type
        mentions = getattr(data.event.message, 'mentions', None)
        logger.info(f"聊天类型: {chat_type}, 提及信息: {mentions}")

        # 调试：详细查看提及信息
        if mentions:
            logger.info(f"提及信息数量: {len(mentions)}")
            for i, mention in enumerate(mentions):
                logger.info(f"提及 {i}: 类型={type(mention)}")
                if hasattr(mention, 'name'):
                    logger.info(f"提及 {i}: name={mention.name}")
                if hasattr(mention, 'key'):
                    logger.info(f"提及 {i}: key={mention.key}")
                # 尝试获取所有属性
                try:
                    attrs = [attr for attr in dir(mention) if not attr.startswith('_')]
                    logger.info(f"提及 {i}: 可用属性={attrs}")
                except Exception as e:
                    logger.warning(f"无法获取提及对象属性: {e}")

        # 检查是否应该跳过处理
        should_skip, skip_reason = MessageParser.should_skip_message(content_data, user_query, chat_type, mentions)
        logger.info(f"跳过检查结果: should_skip={should_skip}, reason={skip_reason}")
        if should_skip:
            logger.info(f"跳过处理: {skip_reason}：{content_data}")
            return

        if not user_query:
            logger.info(f"无需处理: 消息文本为空")
            reply_simple_text_message(message_id, "请输入您的问题。")
            return

        # 清理用户查询
        user_query = MessageParser.clean_user_query(user_query)
        logger.info(f"清理后的用户查询: {user_query}")

        # 验证查询长度（仅对新对话）
        is_new_conversation = (root_id == message_id)
        logger.info(f"是否为新对话: {is_new_conversation}, root_id: {root_id}, message_id: {message_id}")
        if not MessageParser.validate_query_length(user_query, is_new_conversation):
            logger.info(f"用户消息过短: {user_query}")
            reply_text = MessageParser.get_query_too_short_reply()
            reply_simple_text_message(message_id, reply_text)
            return

        # 处理查询
        logger.info(f"开始处理查询: {user_query}")
        try:
            asyncio.create_task(
                QueryProcessor.handle_agent_query(
                    message_id, user_query.strip(), user_info_dict, root_id
                )
            )
            logger.info(f"成功创建Agent查询任务")
        except Exception as e:
            logger.error(f"启动Agent任务时出错: {e}")
            reply_simple_text_message(message_id, f"处理您的请求时遇到错误: {e}")

    @staticmethod
    def sync_wrapper_message_receive(data: lark.im.v1.P2ImMessageReceiveV1) -> None:
        """同步包装器，用于调度异步消息处理器

        Args:
            data: 消息接收数据
        """
        try:
            logger.info(f"同步包装器收到消息: {data.event.message.message_id}")
            # 将异步处理程序调度到事件循环上运行
            asyncio.create_task(EventHandlers.handle_message_receive(data))
        except Exception as e:
            logger.error(f"调度异步消息处理任务时出错: {e}", exc_info=True)


def create_event_handler():
    """创建事件处理器

    Returns:
        事件处理器实例
    """
    return (
        lark.EventDispatcherHandler.builder("", "")
        .register_p2_im_message_receive_v1(EventHandlers.sync_wrapper_message_receive)
        .register_p2_card_action_trigger(EventHandlers.handle_card_action_trigger)
        .build()
    )
